import 'dart:async';

import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class ImageWithLoading extends StatefulWidget {
  final String imageUrl;
  final bool isLoading;

  const ImageWithLoading({
    required this.imageUrl,
    this.isLoading = false,
    super.key,
  });

  @override
  State<ImageWithLoading> createState() => _ImageWithLoadingState();
}

class _ImageWithLoadingState extends State<ImageWithLoading> {
  // bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    setState(() {
      // _isLoading = true;
      _hasError = false;
    });

    final image = NetworkImage(widget.imageUrl);

    final completer = Completer<void>();
    final imageStream = image.resolve(const ImageConfiguration());

    final listener = ImageStreamListener(
      (info, _) {
        completer.complete();
      },
      onError: (error, stackTrace) {
        completer.completeError(error);
        setState(() {
          _hasError = true;
        });
      },
    );

    imageStream.addListener(listener);

    try {
      await completer.future;
    } catch (_) {
      // Error handled in listener
    } finally {
      imageStream.removeListener(listener);
      if (mounted) {
        setState(() {
          // _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        // if (_isLoading) {
        //   return Center(
        //     child: LoadingAnimationWidget.prograssiveDots(
        //       color: colorTheme.onSecondaryColor,
        //       size: prograssiveDotSize,
        //     ),
        //   );
        // }
        if (_hasError) {
          return Container(
            // width: double.infinity,
            width: MediaQuery.sizeOf(context).width * 0.25,
            height: 50,
            color: Colors.grey,
            child: const Center(
              child: Icon(Icons.error), // Placeholder icon for error
            ),
          );
        }

        return Stack(
          children: [
            Image.network(
              widget.imageUrl,
              fit: BoxFit.cover,
              // width: double.infinity,
              // height: 200, // Adjust height as needed

              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;

                return SizedBox(
                  width: width5XLarge,
                  height: heightMedium,
                  child: Center(
                    child: LoadingAnimationWidget.progressiveDots(
                      color: colorTheme.onSecondaryColor,
                      size: prograssiveDotSize,
                    ),
                  ),
                );
              },
            ),
            // if (widget.isLoading)
            //   Container(
            //     color: colorTheme.onPrimaryColor.withOpacity(0.5), // Placeholder color
            //   ),
          ],
        );
      },
    );
  }
}
