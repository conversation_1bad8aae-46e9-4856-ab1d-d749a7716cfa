import 'dart:async';

import 'package:ddone/models/audio_device_model.dart';
import 'package:ddone/models/enums/audio_device_type_enum.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

class AudioDeviceService {
  static final AudioDeviceService _instance = AudioDeviceService._internal();
  AudioDeviceService._internal();
  factory AudioDeviceService() => _instance;

  final StreamController<List<AudioDeviceModel>> _deviceChangeController =
      StreamController<List<AudioDeviceModel>>.broadcast();

  Timer? _devicePollingTimer;
  List<AudioDeviceModel> _lastKnownDevices = [];

  Stream<List<AudioDeviceModel>> get deviceChangeStream => _deviceChangeController.stream;

  /// Dispose the service and clean up resources
  void dispose() {
    log.t('AudioDeviceService: Disposing');
    _devicePollingTimer?.cancel();
    _deviceChangeController.close();
  }

  /// Get all available audio output devices
  Future<List<AudioDeviceModel>> getAvailableDevices() async {
    try {
      List<MediaDeviceInfo> mediaDevices = await Helper.audiooutputs;
      List<AudioDeviceModel> devices = [];

      for (MediaDeviceInfo device in mediaDevices) {
        devices.add(AudioDeviceModel.fromMediaDeviceInfo(
          device.deviceId,
          device.label,
        ));
      }

      if (isDesktop) {
        // Add special "None" option for desktop users to turn off speaker
        devices.add(const AudioDeviceModel(
          deviceId: 'none',
          label: 'None (Turn off speaker)',
          type: AudioDeviceType.none,
        ));
      } else if (isIOS) {
        // in iOS we can only flip between the preferred output (either earpiece or earbuds) and loud speaker.
        // This is a limitation on flutter_webtrc package Helper.selectAudioOutput() method.
        // So here we control it to always only has 2 devices.
        bool hasBuiltInReceiver = devices.any((d) => d.deviceId == kIOSBuiltInRecevierId);
        if (devices.length < 2 && !hasBuiltInReceiver) {
          // add in built-in receiver option if it doesn't exist (happen after user selected speaker)
          devices.add(const AudioDeviceModel(
            deviceId: kIOSBuiltInRecevierId,
            label: 'Receiver',
            type: AudioDeviceType.earpiece,
          ));
        } else if (devices.length > 2 && hasBuiltInReceiver) {
          // remove Built-In Receiver option if it exist together with preferred output
          devices.removeWhere((d) => d.deviceId == kIOSBuiltInRecevierId);
        }
      }

      String devicesListStr = 'AudioDeviceService: Found ${devices.length} audio devices';
      for (var device in devices) {
        devicesListStr += '\n  - ${device.toString()}';
      }
      // log.d(devicesListStr);

      return devices;
    } catch (e) {
      log.e('AudioDeviceService: Failed to get available devices', error: e);
      return [];
    }
  }

  /// Select an audio device
  Future<bool> selectDevice(AudioDeviceModel device) async {
    try {
      log.t('AudioDeviceService: Selecting device ${device.deviceId} (${device.label})');

      if (device.deviceId == 'none') {
        // For desktop users, this means turning off the speaker
        // We'll handle this in the cubit by muting the speaker audio
        return true;
      }

      await Helper.selectAudioOutput(device.deviceId);
      log.t('AudioDeviceService: Successfully selected device ${device.deviceId}');
      return true;
    } catch (e) {
      log.e('AudioDeviceService: Failed to select device ${device.deviceId}', error: e);
      return false;
    }
  }

  /// Get the default device based on platform and device priority
  AudioDeviceModel? getDefaultDevice(List<AudioDeviceModel> devices) {
    if (devices.isEmpty) return null;

    // use priority-based selection
    devices.sort((a, b) => b.type.priority.compareTo(a.type.priority));
    return devices.first;
  }

  /// Start monitoring device changes
  Future<void> startDeviceMonitoring() async {
    if (_devicePollingTimer?.isActive ?? false) return;

    log.t('AudioDeviceService: Start device monitoring');

    // Initial device list
    _lastKnownDevices = await getAvailableDevices();

    // Start polling for device changes
    // Note: WebRTC doesn't provide native device change events, so we poll
    _devicePollingTimer = Timer.periodic(const Duration(seconds: 3), (_) async {
      // << TO CHANGE
      try {
        final currentDevices = await getAvailableDevices();

        // Check if devices have changed
        if (!_areDeviceListsEqual(_lastKnownDevices, currentDevices)) {
          log.t('AudioDeviceService: Device list changed');
          _lastKnownDevices = currentDevices;
          _deviceChangeController.add(currentDevices);
        }
      } catch (e) {
        log.e('AudioDeviceService: Error during device monitoring', error: e);
      }
    });
  }

  Future<void> stopDeviceMonitoring() async {
    log.t('AudioDeviceService: Stop device monitoring');
    _devicePollingTimer?.cancel();
    _devicePollingTimer = null;
  }

  /// Compare two device lists to detect changes
  bool _areDeviceListsEqual(List<AudioDeviceModel> list1, List<AudioDeviceModel> list2) {
    if (list1.length != list2.length) {
      return false;
    }

    // Create copies to avoid modifying the original lists, then sort them.
    final sortedList1 = List<AudioDeviceModel>.from(list1);
    final sortedList2 = List<AudioDeviceModel>.from(list2);

    // Define a custom sort order. We need to be consistent across both lists.
    // Sorting by deviceId, then label, then isAvailable ensures a stable order.
    sortedList1.sort((a, b) {
      int deviceIdComparison = a.deviceId.compareTo(b.deviceId);
      if (deviceIdComparison != 0) return deviceIdComparison;

      int labelComparison = a.label.compareTo(b.label);
      if (labelComparison != 0) return labelComparison;

      return (a.isAvailable ? 1 : 0).compareTo(b.isAvailable ? 1 : 0);
    });

    sortedList2.sort((a, b) {
      int deviceIdComparison = a.deviceId.compareTo(b.deviceId);
      if (deviceIdComparison != 0) return deviceIdComparison;

      int labelComparison = a.label.compareTo(b.label);
      if (labelComparison != 0) return labelComparison;

      return (a.isAvailable ? 1 : 0).compareTo(b.isAvailable ? 1 : 0);
    });

    // Now compare elements at the same index
    for (int i = 0; i < sortedList1.length; i++) {
      if (sortedList1[i].deviceId != sortedList2[i].deviceId ||
          sortedList1[i].label != sortedList2[i].label ||
          sortedList1[i].isAvailable != sortedList2[i].isAvailable) {
        return false;
      }
    }

    return true;
  }

  /// Get device IDs for specific device types
  String getLoudSpeakerDeviceId() {
    if (isIOS) {
      return 'Speaker';
    } else if (isAndroid) {
      return 'speaker';
    }
    return '';
  }

  String getEarpieceDeviceId() {
    if (isIOS) {
      return 'Built-In Receiver';
    } else if (isAndroid) {
      return 'earpiece';
    }
    return '';
  }

  Future<bool> hasInputDevicesAvailable() async {
    try {
      List<MediaDeviceInfo> allDevices = await navigator.mediaDevices.enumerateDevices();
      List<MediaDeviceInfo> inputDevices = allDevices.where((device) => device.kind == 'audioinput').toList();

      bool hasInputs = inputDevices.isNotEmpty;

      if (!hasInputs) {
        log.w('No audio input devices (microphones) found');
      } else {
        log.i('Audio input devices available: ${inputDevices.length} microphones found');
        for (var device in inputDevices) {
          log.d('  - Input device: ${device.label} (${device.deviceId})');
        }
      }

      return hasInputs;
    } catch (e) {
      log.e('Failed to check audio input devices', error: e);
      return false;
    }
  }

  Future<bool> hasMediaDevicesAvailable() async {
    try {
      final hasInputs = await hasInputDevicesAvailable();
      final outputDevices = await getAvailableDevices();
      final realOutputDevices = outputDevices.where((device) => device.deviceId != 'none').toList();
      final hasOutputs = realOutputDevices.isNotEmpty;

      bool hasAllDevices = hasInputs && hasOutputs;

      if (!hasAllDevices) {
        if (!hasInputs) {
          log.w('No audio input devices found for media generation');
        }
        if (!hasOutputs) {
          log.w('No audio output devices found');
        }
      } else {
        log.i('Media devices available: ${realOutputDevices.length} outputs, input devices detected');
      }

      return hasAllDevices;
    } catch (e) {
      log.e('Failed to check media devices', error: e);
      return false;
    }
  }
}
