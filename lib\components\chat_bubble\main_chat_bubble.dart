import 'package:cached_network_image/cached_network_image.dart';
import 'package:ddone/components/chat_bubble/decline_chat_bubble.dart';
import 'package:ddone/components/chat_bubble/document_chat_bubble.dart';
import 'package:ddone/components/chat_bubble/invitation_chat_bubble.dart';
import 'package:ddone/components/chat_bubble/video_chat_bubble.dart';
import 'package:ddone/constants/chat_emoji_constants.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/add_bookmark/add_bookmark_cubit.dart';
import 'package:ddone/cubit/chat/chat_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/models/xmpp_model/mam_info.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:ddone/utils/extensions/string_ext.dart';

class MainChatBubble extends StatelessWidget {
  final Map<String, List<MamInfo>> mamList;
  final String receiver;
  final String sipAccount;

  final LoginCubit loginCubit;
  final MamListCubit mamListCubit;
  final ChatUiCubit chatUiCubit;
  final MessagesCubit messagesCubit;
  final ChatCubit chatCubit;
  final AddBookmarkCubit addBookmarkCubit;
  final BookmarkListCubit bookmarkListCubit;
  final GroupUiCubit groupUiCubit;
  final ContactsCubit contactsCubit;

  const MainChatBubble({
    required this.mamList,
    required this.receiver,
    required this.sipAccount,
    required this.loginCubit,
    required this.mamListCubit,
    required this.chatUiCubit,
    required this.messagesCubit,
    required this.chatCubit,
    required this.addBookmarkCubit,
    required this.bookmarkListCubit,
    required this.groupUiCubit,
    required this.contactsCubit,
    super.key,
  });

  bool isEmoji(String input) {
    // Unicode ranges for emoji characters

    for (final char in input.characters) {
      final codeUnit = char.runes.first;

      bool isEmoji = emojiRanges.any((range) {
        return codeUnit >= range[0] && codeUnit <= range[1];
      });

      if (!isEmoji) {
        return false;
      }
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    // final List<MamInfo>? stateke = mamList[infoState.receiver];

    // if (!infoState.receiver.contains('muc')) {
    List<MamInfo> selectedMessages = mamList[receiver] ?? [];
    List<MamInfo> reverseMessaged = selectedMessages.reversed.toList();
    Set<String> mamInfoSet = {};
    final reverseMessage = reverseMessaged.where((element) {
      debugPrint('element1: ${element.msgId}');
      debugPrint('element2: ${element.sender}');
      debugPrint('element3: ${element.body}');
      return mamInfoSet.add(element.msgId);
    }).toList();

    return reverseMessage.isNotEmpty
        ? ListView.builder(
            // controller: _scrollController,
            reverse: true,
            itemCount: reverseMessage.length,
            itemBuilder: (context, index) {
              final MamInfo mamInfos = reverseMessage[index];
              final messages = mamInfos;
              final bool isMe = messages.sender == '';
              final String grpSender = messages.grpSender;
              final List<String> senderSplit = grpSender.split('@');

              final String parsedTime = DateTime.parse(messages.time).toUtc().toString();
              final DateTime localTime = DateTime.parse(parsedTime).toLocal();
              final String formattedTime = DateFormat('hh:mm a').format(localTime);

              // 2024-04-11
              final int currentYear = DateTime.now().year;
              // final String formattedDate = (localTime.year == currentYear)
              //     ? DateFormat('MM-dd').format(localTime)
              //     : DateFormat('yyyy-MM-dd').format(localTime);

              final String trimmedBody = messages.body.trim();
              final Uri? fileUri = Uri.tryParse(messages.body);
              final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp)$', caseSensitive: true);
              final RegExp videoExtensions = RegExp(r'\.(mp4|mkv)$', caseSensitive: true);
              final isVideo = videoExtensions.hasMatch(trimmedBody);
              final isImage = imageExtensions.hasMatch(trimmedBody);

              final fileName = fileUri?.pathSegments.isNotEmpty == true ? fileUri?.pathSegments.last : '';

              final RegExp groupRegex = RegExp(r'(.+)\s(\S+@muc\.x001\.dotdashtech\.com)', caseSensitive: true);
              final groupMatch = groupRegex.firstMatch(messages.body);
              final inviteText = groupMatch?.group(1);
              final inviteSplit = inviteText?.split(invitationRegex);
              final groupOnly = groupMatch?.group(2);
              final groupName = groupOnly?.mucIDFilter();

              // final groupDomain = groupSplit?[1];

              bool isPreviousMessageSentByMe(int currentIndex) {
                if (currentIndex <= 0 || currentIndex >= reverseMessage.length) {
                  return false;
                }
                return reverseMessage[currentIndex - 1].sender == '';
              }

              if (messages.body.isNotEmpty) {
                return BlocBuilder<ThemeCubit, ThemeState>(
                  builder: (context, themeState) {
                    final colorTheme = themeState.colorTheme;
                    final textTheme = themeState.themeData.textTheme;

                    final currentMessage = reverseMessage[index];
                    final currentDateTime = DateTime.parse(currentMessage.time).toUtc().toLocal();
                    final String currentDate = DateFormat('yyyy-MM-dd').format(currentDateTime);
                    final String currentMonthDay = DateFormat('MM-dd').format(currentDateTime);
                    final String currentDay = DateFormat('EEE').format(currentDateTime);

                    String? previousDate;

                    if (index + 1 < reverseMessage.length) {
                      final previousMessage = reverseMessage[index + 1];
                      final previousDateTime = DateTime.parse(previousMessage.time).toUtc().toLocal();
                      previousDate = DateFormat('yyyy-MM-dd').format(previousDateTime);
                    }

                    bool sholdShowDate = false;

                    if (previousDate == null || currentDate != previousDate) {
                      sholdShowDate = true;
                      previousDate = currentDate;
                    }

                    final chatDate = (currentDateTime.year) == currentYear ? currentMonthDay : currentDate;

                    return Column(
                      children: [
                        if (sholdShowDate)
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                    5,
                                  ),
                                  color: colorTheme.roundShapeInkWellColor,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    '$chatDate ($currentDay)',
                                    style: textTheme.bodySmall!.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: colorTheme.onPrimaryColor.withOpacity(0.8),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        Padding(
                          padding: EdgeInsets.only(
                            top: 3.0,
                            right: isMe ? 10.0 : 0.0,
                            left: isMe ? 0.0 : 10.0,
                          ),
                          child:
                              // Row(
                              //   mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
                              //   children: [
                              // isMe
                              //     ? Column(
                              //         children: [
                              //           IconButton(
                              //             onPressed: () {},
                              //             icon: Icon(
                              //               Icons.menu,
                              //             ),
                              //           ),
                              //         ],
                              //       )
                              //     : Container(),
                              !messages.body.contains(invitationDeclineRegex)
                                  ? Column(
                                      crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                                      children: [
                                        isMe && !messages.to.contains('muc') || !messages.sender.contains('muc')
                                            ? Container()
                                            : Row(
                                                mainAxisAlignment:
                                                    isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
                                                children:
                                                    // messageDate
                                                    [
                                                  // messages.to.contains('conference') ||
                                                  //         messages.sender
                                                  //             .contains('conference')
                                                  //     ?
                                                  Text(
                                                    senderSplit[0],
                                                    style: const TextStyle(
                                                      color: Colors.white54,
                                                    ),
                                                  )
                                                  // : const Text(''),
                                                ],
                                              ),
                                        ConstrainedBox(
                                          constraints: const BoxConstraints(
                                            maxWidth: width8XLarge,
                                            // maxHeight: MediaQuery.of(context).size.height * 0.3,
                                          ),
                                          child: IntrinsicWidth(
                                            child: Container(
                                              // height: 45,
                                              padding: !isImage
                                                  ? const EdgeInsets.only(
                                                      left: 10,
                                                      right: 3,
                                                      bottom: 7,
                                                      top: 7,
                                                    )
                                                  : const EdgeInsets.symmetric(
                                                      horizontal: 5,
                                                      vertical: 5,
                                                    ),
                                              decoration: BoxDecoration(
                                                color: isMe ? Colors.amber : Colors.black54,
                                                borderRadius: isMe
                                                    ? const BorderRadius.only(
                                                        topLeft: Radius.circular(7),
                                                        bottomLeft: Radius.circular(7),
                                                        topRight: Radius.circular(7),
                                                      )
                                                    : const BorderRadius.only(
                                                        topLeft: Radius.circular(7),
                                                        bottomRight: Radius.circular(7),
                                                        topRight: Radius.circular(7),
                                                      ),
                                              ),
                                              child: Column(
                                                children: [
                                                  Row(
                                                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    // crossAxisAlignment: CrossAxisAlignment.end,
                                                    // mainAxisAlignment: MainAxisAlignment.start,
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          mainAxisAlignment: MainAxisAlignment.start,
                                                          children: [
                                                            if (!messages.body.contains(env!.minioUrl) &&
                                                                !messages.body.contains(invitationRegex) &&
                                                                !messages.body.contains(invitationDeclineRegex) &&
                                                                !isEmoji(messages.body) &&
                                                                !isImage)
                                                              Text(
                                                                messages.body,
                                                                style: TextStyle(
                                                                  color: isMe ? Colors.black : Colors.white70,
                                                                  // fontSize: 14,
                                                                ),
                                                                maxLines: null,
                                                              ),
                                                            if (isEmoji(messages.body) && !isImage)
                                                              for (final char in messages.body.characters)
                                                                Container(
                                                                  decoration: BoxDecoration(
                                                                    borderRadius: const BorderRadius.all(
                                                                      Radius.circular(
                                                                        3,
                                                                      ),
                                                                    ),
                                                                    color: colorTheme.primaryVariantColor,
                                                                  ),
                                                                  child: Padding(
                                                                    padding: const EdgeInsets.only(bottom: 1.0),
                                                                    child: Text(
                                                                      char,
                                                                      style: TextStyle(
                                                                        color: isMe ? Colors.black : Colors.white70,
                                                                        fontSize: 16,
                                                                      ),
                                                                      maxLines: null,
                                                                    ),
                                                                  ),
                                                                ),
                                                            if (isImage)
                                                              Stack(
                                                                children: [
                                                                  // ImageWithLoading(
                                                                  //   imageUrl: trimmedBody,
                                                                  //   isLoading: true, // Initially show loading
                                                                  // ),
                                                                  MouseRegion(
                                                                    cursor: SystemMouseCursors.click,
                                                                    child: GestureDetector(
                                                                      onTap: () {
                                                                        imagePreviewDialog(
                                                                          context: context,
                                                                          imageUrl: trimmedBody,
                                                                          // colorThemes: colorTheme,
                                                                        );
                                                                      },
                                                                      child: CachedNetworkImage(
                                                                        imageUrl: trimmedBody,
                                                                        placeholder: (context, url) => SizedBox(
                                                                          width: width5XLarge,
                                                                          height: heightMedium,
                                                                          child: Center(
                                                                            child:
                                                                                LoadingAnimationWidget.progressiveDots(
                                                                              color: colorTheme.onSecondaryColor,
                                                                              size: prograssiveDotSize,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                        errorWidget: (context, url, error) {
                                                                          return Container(
                                                                            width:
                                                                                MediaQuery.sizeOf(context).width * 0.25,
                                                                            height: 50,
                                                                            color: Colors.grey,
                                                                            child: const Center(
                                                                              child: Icon(
                                                                                // Placeholder icon for error
                                                                                Icons.error,
                                                                              ),
                                                                            ),
                                                                          );
                                                                        },
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  Positioned(
                                                                    right: 0,
                                                                    bottom: 0,
                                                                    child: Container(
                                                                      padding: const EdgeInsets.only(
                                                                        left: 5,
                                                                        top: 2,
                                                                        bottom: 2,
                                                                        right: 2,
                                                                      ),
                                                                      decoration: BoxDecoration(
                                                                        color: Colors.black.withOpacity(0.35),
                                                                        // borderRadius: BorderRadius.circular(20),
                                                                      ),
                                                                      // constraints: const BoxConstraints(
                                                                      //   minWidth: 15,
                                                                      //   minHeight: 5,
                                                                      // ),
                                                                      child: Text(
                                                                        formattedTime,
                                                                        style: const TextStyle(
                                                                          color: Colors.white70,
                                                                          fontSize: 10,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            if (isVideo && !isImage)
                                                              VideoChatBubble(
                                                                videoUrl: trimmedBody,
                                                              ),
                                                            if (!isImage &&
                                                                messages.body.contains(env!.minioUrl) &&
                                                                !isVideo)
                                                              DocumentChatBubble(
                                                                fileName: fileName!,
                                                                isMe: isMe,
                                                                formattedTime: formattedTime,
                                                                fileUrl: fileUri.toString(),
                                                              ),
                                                            if (!isImage && (messages.body.contains(invitationRegex)))
                                                              BlocBuilder<BookmarkListCubit, BookmarkListState>(
                                                                builder: (context, state) {
                                                                  return InvitationChatBubble(
                                                                    onDeclinePressed: () async {
                                                                      // await declineGroupJoin(
                                                                      //   JID.fromString(groupOnly),
                                                                      //   JID.fromString(ChatPage.receiver.toString()),
                                                                      //   InvitationChatBubble
                                                                      //       .declinereasonEditingController.text,
                                                                      // );

                                                                      await messagesCubit.sendMessage(
                                                                        receiverJid: receiver,
                                                                        loginCubit: loginCubit,
                                                                        mamListCubit: mamListCubit,
                                                                        chatUiCubit: chatUiCubit,
                                                                        contactsCubit: contactsCubit,
                                                                        text:
                                                                            '${messages.msgId} [~InvitationDeclined~] Invitation to $groupOnly has been declined ',
                                                                        // isSender: true,
                                                                        // sender: 'You',
                                                                      );
                                                                      // pop();
                                                                    },
                                                                    onAcceptPressed: () async {
                                                                      await chatCubit.joinChatRoom(
                                                                        groupOnly.toString(),
                                                                        sipAccount,
                                                                        loginCubit,
                                                                        addBookmarkCubit,
                                                                        bookmarkListCubit,
                                                                        groupUiCubit,
                                                                      );
                                                                      groupUiCubit.update();
                                                                    },
                                                                    groupOnly: groupOnly ?? '',
                                                                    isMe: isMe,
                                                                    inviteSplit: inviteSplit!,
                                                                    groupName: groupName ?? '',
                                                                    date: localTime,
                                                                    time: formattedTime,
                                                                    body: messages.body,
                                                                    id: messages.msgId,
                                                                  );
                                                                },
                                                              ),
                                                          ],
                                                        ),
                                                      ),
                                                      if (!messages.body.contains(invitationRegex) &&
                                                          !messages.body.contains(env!.minioUrl) &&
                                                          !isImage)
                                                        const Column(
                                                          children: [
                                                            SizedBox(
                                                              width: 20,
                                                            )
                                                          ],
                                                        ),
                                                    ],
                                                  ),
                                                  if (!messages.body.contains(invitationRegex) &&
                                                      !messages.body.contains(env!.minioUrl) &&
                                                      !isImage)
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.end,
                                                      children: [
                                                        SizedBox(width: !isImage ? 8.0 : 0),
                                                        // if (!messages.body.contains(invitationRegex) &&
                                                        //     !messages.body.contains(env!.minioUrl))
                                                        Text(
                                                          !isImage ? formattedTime : '',
                                                          style: TextStyle(
                                                            color: isMe ? Colors.black : Colors.white70,
                                                            fontSize: 10,
                                                          ),
                                                        ),
                                                        if (!messages.body.contains('[~Invitation~]'))
                                                          SizedBox(
                                                            width: !isImage ? 4.0 : 0,
                                                          ),
                                                      ],
                                                    ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    )
                                  : Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        if (!isImage && (messages.body.contains(invitationDeclineRegex)))
                                          BlocBuilder<BookmarkListCubit, BookmarkListState>(
                                            builder: (context, state) {
                                              return DeclineChatBubble(
                                                groupOnly: groupOnly ?? '',
                                                isMe: isMe,
                                                // inviteSplit: inviteSplit!,
                                                groupName: groupName ?? '',
                                                date: localTime,
                                                time: formattedTime,
                                                body: messages.body,
                                              );
                                            },
                                          ),
                                      ],
                                    ),
                          // isMe
                          //     ? Container()
                          //     : Column(
                          //         children: [
                          //           IconButton(
                          //             onPressed: () async {
                          //               print("${messages.body} ${messages.msgId}");
                          //               LoginAuthenticated currentLoginState =
                          //                   context.read<LoginCubit>().state as LoginAuthenticated;

                          //               final retract = currentLoginState.connection
                          //                   .getManagerById<MessageRetractionManager>(messageRetractionManager)!;

                          //               await retract.retractMessage(messages.msgId, messages.to);
                          //             },
                          //             icon: Icon(
                          //               Icons.menu,
                          //             ),
                          //           ),
                          //         ],
                          //       ),
                          //   ],
                          // ),
                        ),
                      ],
                    );
                  },
                );
              } else {
                return Container();
              }
            },
          )
        : Container();
    // } else {
    //   List<MamInfo> selectedGroupMessages = mamListState.mamgroupList[infoState.receiver] ?? [];
    //   List<MamInfo> reverseGroupMessaged = selectedGroupMessages.reversed.toList();
    //   Set<String> mamInfoSet = {};
    //   final reverseMessage = reverseGroupMessaged.where((element) {
    //     print('element1: ${element.msgId}');
    //     print('element2: ${element.sender}');
    //     print('element3: ${element.body}');
    //     return mamInfoSet.add(element.msgId);
    //   }).toList();
    //   return reverseMessage.length != null || reverseMessage.isNotEmpty
    //       ? ListView.builder(
    //           // controller: _scrollController,
    //           reverse: true,
    //           itemCount: reverseMessage.length,
    //           itemBuilder: (context, index) {
    //             final MamInfo mamInfos = reverseMessage[index];
    //             final messages = mamInfos;
    //             final bool isMe = messages.sender == '';
    //             final String grpSender = messages.grpSender;
    //             final List<String> senderSplit = grpSender.split('@');

    //             final String parsedTime = DateTime.parse(messages.time).toUtc().toString();
    //             final DateTime localTime = DateTime.parse(parsedTime).toLocal();
    //             final String formattedTime = DateFormat('hh:mm a').format(localTime);

    //             // 2024-04-11
    //             final int currentYear = DateTime.now().year;
    //             // final String formattedDate = (localTime.year == currentYear)
    //             //     ? DateFormat('MM-dd').format(localTime)
    //             //     : DateFormat('yyyy-MM-dd').format(localTime);

    //             final String trimmedBody = messages.body.trim();
    //             final Uri? fileUri = Uri.tryParse(messages.body);
    //             final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp)$', caseSensitive: true);
    //             final RegExp videoExtensions = RegExp(r'\.(mp4|mkv)$', caseSensitive: true);
    //             final isVideo = videoExtensions.hasMatch(trimmedBody);
    //             final isImage = imageExtensions.hasMatch(trimmedBody);

    //             final fileName = fileUri?.pathSegments.isNotEmpty == true ? fileUri?.pathSegments.last : "";

    //             final RegExp groupRegex =
    //                 RegExp(r'(.+)\s(\S+@muc\.x001\.dotdashtech\.com)', caseSensitive: true);
    //             final groupMatch = groupRegex.firstMatch(messages.body);
    //             final inviteText = groupMatch?.group(1);
    //             final inviteSplit = inviteText?.split(invitationRegex);
    //             final groupOnly = groupMatch?.group(2);
    //             final groupName = groupOnly?.mucIDFilter();

    //             // final groupDomain = groupSplit?[1];

    //             bool isPreviousMessageSentByMe(int currentIndex) {
    //               if (currentIndex <= 0 || currentIndex >= reverseMessage.length) {
    //                 return false;
    //               }
    //               return reverseMessage[currentIndex - 1].sender == '';
    //             }

    //             if (messages.body.isNotEmpty) {
    //               return BlocBuilder<ThemeCubit, ThemeState>(
    //                 builder: (context, themeState) {
    //                   final colorTheme = themeState.colorTheme;
    //                   final textTheme = themeState.themeData.textTheme;

    //                   final currentMessage = reverseMessage[index];
    //                   final currentDateTime = DateTime.parse(currentMessage.time).toUtc().toLocal();
    //                   final String currentDate = DateFormat('yyyy-MM-dd').format(currentDateTime);
    //                   final String currentMonthDay = DateFormat('MM-dd').format(currentDateTime);
    //                   final String currentDay = DateFormat('EEE').format(currentDateTime);

    //                   String? previousDate;

    //                   if (index + 1 < reverseMessage.length) {
    //                     final previousMessage = reverseMessage[index + 1];
    //                     final previousDateTime = DateTime.parse(previousMessage.time).toUtc().toLocal();
    //                     previousDate = DateFormat('yyyy-MM-dd').format(previousDateTime);
    //                   }

    //                   bool sholdShowDate = false;

    //                   if (previousDate == null || currentDate != previousDate) {
    //                     sholdShowDate = true;
    //                     previousDate = currentDate;
    //                   }

    //                   final chatDate = (currentDateTime.year) == currentYear ? currentMonthDay : currentDate;

    //                   return Column(
    //                     children: [
    //                       if (sholdShowDate)
    //                         Center(
    //                           child: Padding(
    //                             padding: const EdgeInsets.symmetric(vertical: 8.0),
    //                             child: Container(
    //                               decoration: BoxDecoration(
    //                                 borderRadius: BorderRadius.circular(
    //                                   5,
    //                                 ),
    //                                 color: colorTheme.onSurfaceColor,
    //                               ),
    //                               child: Padding(
    //                                 padding: const EdgeInsets.all(8.0),
    //                                 child: Text(
    //                                   "$chatDate ($currentDay)",
    //                                   style: textTheme.bodySmall!.copyWith(
    //                                     fontWeight: FontWeight.bold,
    //                                     color: colorTheme.onPrimaryColor.withOpacity(0.8),
    //                                   ),
    //                                 ),
    //                               ),
    //                             ),
    //                           ),
    //                         ),
    //                       Padding(
    //                         padding: EdgeInsets.only(
    //                           top: 3.0,
    //                           right: isMe ? 10.0 : 0.0,
    //                           left: isMe ? 0.0 : 10.0,
    //                         ),
    //                         child:
    //                             // Row(
    //                             //   mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
    //                             //   children: [
    //                             // isMe
    //                             //     ? Column(
    //                             //         children: [
    //                             //           IconButton(
    //                             //             onPressed: () {},
    //                             //             icon: Icon(
    //                             //               Icons.menu,
    //                             //             ),
    //                             //           ),
    //                             //         ],
    //                             //       )
    //                             //     : Container(),
    //                             !messages.body.contains(invitationDeclineRegex)
    //                                 ? Column(
    //                                     crossAxisAlignment:
    //                                         isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
    //                                     children: [
    //                                       isMe && !messages.to.contains('muc') ||
    //                                               !messages.sender.contains('muc')
    //                                           ? Container()
    //                                           : Row(
    //                                               mainAxisAlignment:
    //                                                   isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
    //                                               children:
    //                                                   // messageDate
    //                                                   [
    //                                                 // messages.to.contains('conference') ||
    //                                                 //         messages.sender
    //                                                 //             .contains('conference')
    //                                                 //     ?
    //                                                 Text(
    //                                                   senderSplit[0],
    //                                                   style: const TextStyle(
    //                                                     color: Colors.white54,
    //                                                   ),
    //                                                 )
    //                                                 // : const Text(''),
    //                                               ],
    //                                             ),
    //                                       ConstrainedBox(
    //                                         constraints: const BoxConstraints(
    //                                           maxWidth: width8XLarge,
    //                                           // maxHeight: MediaQuery.of(context).size.height * 0.3,
    //                                         ),
    //                                         child: IntrinsicWidth(
    //                                           child: Container(
    //                                             // height: 45,
    //                                             padding: !isImage
    //                                                 ? const EdgeInsets.only(
    //                                                     left: 10,
    //                                                     right: 3,
    //                                                     bottom: 7,
    //                                                     top: 7,
    //                                                   )
    //                                                 : const EdgeInsets.symmetric(
    //                                                     horizontal: 5,
    //                                                     vertical: 5,
    //                                                   ),
    //                                             decoration: BoxDecoration(
    //                                               color: isMe ? Colors.amber : Colors.black54,
    //                                               borderRadius: isMe
    //                                                   ? const BorderRadius.only(
    //                                                       topLeft: Radius.circular(7),
    //                                                       bottomLeft: Radius.circular(7),
    //                                                       topRight: Radius.circular(7),
    //                                                     )
    //                                                   : const BorderRadius.only(
    //                                                       topLeft: Radius.circular(7),
    //                                                       bottomRight: Radius.circular(7),
    //                                                       topRight: Radius.circular(7),
    //                                                     ),
    //                                             ),
    //                                             child: Column(
    //                                               children: [
    //                                                 Row(
    //                                                   // mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //                                                   // crossAxisAlignment: CrossAxisAlignment.end,
    //                                                   // mainAxisAlignment: MainAxisAlignment.start,
    //                                                   mainAxisSize: MainAxisSize.min,
    //                                                   children: [
    //                                                     Expanded(
    //                                                       child: Column(
    //                                                         crossAxisAlignment: CrossAxisAlignment.start,
    //                                                         mainAxisAlignment: MainAxisAlignment.start,
    //                                                         children: [
    //                                                           if (!messages.body.contains(
    //                                                                   env!.minioUrl) &&
    //                                                               !messages.body.contains(invitationRegex) &&
    //                                                               !messages.body
    //                                                                   .contains(invitationDeclineRegex) &&
    //                                                               !isEmoji(messages.body) &&
    //                                                               !isImage)
    //                                                             Text(
    //                                                               messages.body,
    //                                                               style: TextStyle(
    //                                                                 color: isMe ? Colors.black : Colors.white70,
    //                                                                 // fontSize: 14,
    //                                                               ),
    //                                                               maxLines: null,
    //                                                             ),
    //                                                           if (isEmoji(messages.body) && !isImage)
    //                                                             for (final char in messages.body.characters)
    //                                                               Container(
    //                                                                 decoration: BoxDecoration(
    //                                                                   borderRadius: const BorderRadius.all(
    //                                                                     Radius.circular(
    //                                                                       3,
    //                                                                     ),
    //                                                                   ),
    //                                                                   color: colorTheme.primaryVariantColor,
    //                                                                 ),
    //                                                                 child: Padding(
    //                                                                   padding:
    //                                                                       const EdgeInsets.only(bottom: 1.0),
    //                                                                   child: Text(
    //                                                                     char,
    //                                                                     style: TextStyle(
    //                                                                       color: isMe
    //                                                                           ? Colors.black
    //                                                                           : Colors.white70,
    //                                                                       fontSize: 16,
    //                                                                     ),
    //                                                                     maxLines: null,
    //                                                                   ),
    //                                                                 ),
    //                                                               ),
    //                                                           if (isImage)
    //                                                             Stack(
    //                                                               children: [
    //                                                                 // ImageWithLoading(
    //                                                                 //   imageUrl: trimmedBody,
    //                                                                 //   isLoading: true, // Initially show loading
    //                                                                 // ),
    //                                                                 MouseRegion(
    //                                                                   cursor: SystemMouseCursors.click,
    //                                                                   child: GestureDetector(
    //                                                                     onTap: () {
    //                                                                       imagePreviewDialog(
    //                                                                         context: context,
    //                                                                         imageUrl: trimmedBody,
    //                                                                         // colorThemes: colorTheme,
    //                                                                       );
    //                                                                     },
    //                                                                     child: CachedNetworkImage(
    //                                                                       imageUrl: trimmedBody,
    //                                                                       placeholder: (context, url) =>
    //                                                                           SizedBox(
    //                                                                         width: width5XLarge,
    //                                                                         height: heightMedium,
    //                                                                         child: Center(
    //                                                                           child: LoadingAnimationWidget
    //                                                                               .prograssiveDots(
    //                                                                             color:
    //                                                                                 colorTheme.onSecondaryColor,
    //                                                                             size: prograssiveDotSize,
    //                                                                           ),
    //                                                                         ),
    //                                                                       ),
    //                                                                       errorWidget: (context, url, error) {
    //                                                                         return Container(
    //                                                                           width: MediaQuery.sizeOf(context)
    //                                                                                   .width *
    //                                                                               0.25,
    //                                                                           height: 50,
    //                                                                           color: Colors.grey,
    //                                                                           child: const Center(
    //                                                                             child: Icon(
    //                                                                               // Placeholder icon for error
    //                                                                               Icons.error,
    //                                                                             ),
    //                                                                           ),
    //                                                                         );
    //                                                                       },
    //                                                                     ),
    //                                                                   ),
    //                                                                 ),
    //                                                                 Positioned(
    //                                                                   right: 0,
    //                                                                   bottom: 0,
    //                                                                   child: Container(
    //                                                                     padding: const EdgeInsets.only(
    //                                                                       left: 5,
    //                                                                       top: 2,
    //                                                                       bottom: 2,
    //                                                                       right: 2,
    //                                                                     ),
    //                                                                     decoration: BoxDecoration(
    //                                                                       color: Colors.black.withOpacity(0.35),
    //                                                                       // borderRadius: BorderRadius.circular(20),
    //                                                                     ),
    //                                                                     // constraints: const BoxConstraints(
    //                                                                     //   minWidth: 15,
    //                                                                     //   minHeight: 5,
    //                                                                     // ),
    //                                                                     child: Text(
    //                                                                       formattedTime,
    //                                                                       style: const TextStyle(
    //                                                                         color: Colors.white70,
    //                                                                         fontSize: 10,
    //                                                                       ),
    //                                                                     ),
    //                                                                   ),
    //                                                                 ),
    //                                                               ],
    //                                                             ),
    //                                                           if (isVideo && !isImage)
    //                                                             VideoChatBubble(
    //                                                               videoFile: trimmedBody,
    //                                                             ),
    //                                                           if (!isImage &&
    //                                                               messages.body.contains(
    //                                                                   env!.minioUrl) &&
    //                                                               !isVideo)
    //                                                             DocumentChatBubble(
    //                                                               fileName: fileName!,
    //                                                               isMe: isMe,
    //                                                               formattedTime: formattedTime,
    //                                                               fileUrl: fileUri.toString(),
    //                                                             ),
    //                                                           if (!isImage &&
    //                                                               (messages.body.contains(invitationRegex)))
    //                                                             BlocBuilder<BookmarkListCubit,
    //                                                                 BookmarkListState>(
    //                                                               builder: (context, state) {
    //                                                                 return InvitationChatBubble(
    //                                                                   onDeclinePressed: () async {
    //                                                                     // await declineGroupJoin(
    //                                                                     //   JID.fromString(groupOnly),
    //                                                                     //   JID.fromString(ChatPage.receiver.toString()),
    //                                                                     //   InvitationChatBubble
    //                                                                     //       .declinereasonEditingController.text,
    //                                                                     // );

    //                                                                     await messagesCubit.sendMessage(
    //                                                                       receiverJid: infoState.receiver,
    //                                                                       loginCubit: loginCubit,
    //                                                                       mamListCubit: mamListCubit,
    //                                                                       chatUiCubit: chatUiCubit,
    //                                                                       text:
    //                                                                           "${messages.msgId} [~InvitationDeclined~] Invitation to $groupOnly has been declined ",
    //                                                                       // isSender: true,
    //                                                                       // sender: 'You',
    //                                                                     );
    //                                                                     // pop();
    //                                                                   },
    //                                                                   onAcceptPressed: () async {
    //                                                                     await chatCubit.joinChatRoom(
    //                                                                       groupOnly.toString(),
    //                                                                       sipAccount,
    //                                                                       loginCubit,
    //                                                                       addBookmarkCubit,
    //                                                                       bookmarkListCubit,
    //                                                                       groupUiCubit,
    //                                                                     );
    //                                                                     groupUiCubit.update();
    //                                                                   },
    //                                                                   groupOnly: groupOnly ?? "",
    //                                                                   isMe: isMe,
    //                                                                   inviteSplit: inviteSplit!,
    //                                                                   groupName: groupName ?? "",
    //                                                                   date: localTime,
    //                                                                   time: formattedTime,
    //                                                                   body: messages.body,
    //                                                                   id: messages.msgId,
    //                                                                 );
    //                                                               },
    //                                                             ),
    //                                                         ],
    //                                                       ),
    //                                                     ),
    //                                                     if (!messages.body.contains(invitationRegex) &&
    //                                                         !messages.body
    //                                                             .contains(env!.minioUrl) &&
    //                                                         !isImage)
    //                                                       const Column(
    //                                                         children: [
    //                                                           SizedBox(
    //                                                             width: 20,
    //                                                           )
    //                                                         ],
    //                                                       ),
    //                                                   ],
    //                                                 ),
    //                                                 if (!messages.body.contains(invitationRegex) &&
    //                                                     !messages.body
    //                                                         .contains(env!.minioUrl) &&
    //                                                     !isImage)
    //                                                   Row(
    //                                                     mainAxisAlignment: MainAxisAlignment.end,
    //                                                     children: [
    //                                                       SizedBox(width: !isImage ? 8.0 : 0),
    //                                                       // if (!messages.body.contains(invitationRegex) &&
    //                                                       //     !messages.body.contains(env!.minioUrl))
    //                                                       Text(
    //                                                         !isImage ? formattedTime : '',
    //                                                         style: TextStyle(
    //                                                           color: isMe ? Colors.black : Colors.white70,
    //                                                           fontSize: 10,
    //                                                         ),
    //                                                       ),
    //                                                       if (!messages.body.contains('[~Invitation~]'))
    //                                                         SizedBox(
    //                                                           width: !isImage ? 4.0 : 0,
    //                                                         ),
    //                                                     ],
    //                                                   ),
    //                                               ],
    //                                             ),
    //                                           ),
    //                                         ),
    //                                       ),
    //                                     ],
    //                                   )
    //                                 : Column(
    //                                     mainAxisAlignment: MainAxisAlignment.center,
    //                                     mainAxisSize: MainAxisSize.min,
    //                                     children: [
    //                                       if (!isImage && (messages.body.contains(invitationDeclineRegex)))
    //                                         BlocBuilder<BookmarkListCubit, BookmarkListState>(
    //                                           builder: (context, state) {
    //                                             return DeclineChatBubble(
    //                                               groupOnly: groupOnly ?? "",
    //                                               isMe: isMe,
    //                                               // inviteSplit: inviteSplit!,
    //                                               groupName: groupName ?? "",
    //                                               date: localTime,
    //                                               time: formattedTime,
    //                                               body: messages.body,
    //                                             );
    //                                           },
    //                                         ),
    //                                     ],
    //                                   ),
    //                         // isMe
    //                         //     ? Container()
    //                         //     : Column(
    //                         //         children: [
    //                         //           IconButton(
    //                         //             onPressed: () async {
    //                         //               print("${messages.body} ${messages.msgId}");
    //                         //               LoginAuthenticated currentLoginState =
    //                         //                   context.read<LoginCubit>().state as LoginAuthenticated;

    //                         //               final retract = currentLoginState.connection
    //                         //                   .getManagerById<MessageRetractionManager>(messageRetractionManager)!;

    //                         //               await retract.retractMessage(messages.msgId, messages.to);
    //                         //             },
    //                         //             icon: Icon(
    //                         //               Icons.menu,
    //                         //             ),
    //                         //           ),
    //                         //         ],
    //                         //       ),
    //                         //   ],
    //                         // ),
    //                       ),
    //                     ],
    //                   );
    //                 },
    //               );
    //             } else {
    //               return Container();
    //             }
    //           },
    //         )
    //       : Container();
    // }
  }
}
