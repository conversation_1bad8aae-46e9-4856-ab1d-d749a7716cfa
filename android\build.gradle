buildscript {
    ext.kotlin_version = '1.9.21'
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }

    dependencies {
        // START: FlutterFire Configuration
        classpath 'com.google.gms:google-services:4.4.2'
        // END: FlutterFire Configuration
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }

    subprojects {
        afterEvaluate { project ->
            if (project.hasProperty('android')) {
                project.android {
                    if (namespace == null) {
                        namespace project.group
                    }
               }
           }
      }
   }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
